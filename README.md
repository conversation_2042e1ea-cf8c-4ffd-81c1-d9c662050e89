# FlyFit

## What is FlyFit?

## What is FlyFit?

FlyFit is a mobile fitness app that helps people start their health and wellness journey through social challenges, personalized workouts, and engaging content. Built with React Native and Firebase, it connects users with trainers and peers to make fitness fun and sustainable.

### Our Mission

Fly Bodies is on a mission to address the obesity epidemic in the US, and FlyFit is the starting point to get people moving. Through social challenges, personalized workouts, and health content, we help people build lasting healthy habits. Our vision is to make FlyFit a one-stop-shop for your entire health and wellness journey.

### Who Uses FlyFit?

- **Fitness Enthusiasts**: Track progress, join challenges, and stay motivated
- **Beginners**: Get started with guided workouts and supportive community
- **Trainers/Coaches**: Manage clients, create challenges, and send personalized workouts
- **Organizations**: Run company-wide wellness programs and challenges

### Key Features

**For Users:**

- 📊 Health data tracking (steps, mileage, weight)
- 🏆 Team-based challenges with leaderboards
- 📚 Daily wellness content (blogs, quizzes, quotes)
- 💪 Workout and meal tracking
- 🔥 Movement streaks and achievements
- 💬 Social feeds and team communication

**For Trainers & Organizations:**

- 👥 Client management and user creation
- 🎯 Challenge creation and management
- 📋 Sending clients workouts
- 📈 Progress tracking and reporting
- 🏢 Organization-wide wellness programs
- 📊 Summary Reports

## Quick Start

Want to see FlyFit in action? Download and launch the app from the app store to start playing with it:

- [Apple App Store](https://apps.apple.com/us/app/fly-fit-fitness/id6450652563)
- [Google Play Store](https://play.google.com/store/apps/details?id=com.flybodies.flyfit&hl=en&gl=US)

> [!NOTE]
> __Want to run the project?__ Start with our [Getting Started Guide](docs/GETTING_STARTED.md) for detailed setup instructions.

## Architecture Overview

FlyFit follows a modern mobile app architecture:

- **Frontend**: React Native with Expo for cross-platform mobile development
- **State Management**: Jotai for global state, React Query for server state
- **Backend**: Firebase (Firestore, Functions, Auth, Storage)
- **Real-time Features**: Firestore real-time listeners
- **External Integrations**: Health data sync (Apple Health, Google Fit)

```mermaid
graph TD
    A["Mobile App <br/> (React Native)"]
    B["Web Sign-up <br/> (React)"]
    C["Link Redirect <br/> (React)"]
    D["PDF Reports <br/> (React PDF)"]
    F["Firebase Backend <br/> (Firestore, Functions, <br/> Auth, Storage, Hosting)"]

    A --> F
    B --> F
    C --> F
    D --> F
```

> [!NOTE]
> **Want to dive deeper?** Check out our [Architecture Documentation](docs/ARCHITECTURE.md) for detailed technical information.

## Repository Structure

```bash
fly-fit/
├── 📱 src/         # React Native mobile app
├── ⚡ functions/   # Firebase backend & APIs
├── 🌐 web-sign-up/  # Web registration app
├── 📚 docs/        # Documentation
└── ️️️🛠️ scripts/     # Build & utility scripts
```

> **First time exploring?** Focus on `src/` for mobile app code and `functions/src/` for backend logic.

## Development Workflow

> [!NOTE]
> __Ready to develop?__ See our [Getting Started Guide](docs/GETTING_STARTED.md) for setup and [Contributing Guidelines](docs/CONTRIBUTING.md) for workflow details.

## Documentation Index

### 🚀 Getting Started

- [__Getting Started Guide__](docs/GETTING_STARTED.md) - Complete setup instructions for new developers
- [**Contributing Guidelines**](docs/CONTRIBUTING.md) - Development workflow, coding standards, and PR process
- [**Project Links**](docs/OPERATIONS.md#project-links) - All important project URLs and dashboards

### 🏗️ Technical Documentation

- [**Architecture Overview**](docs/ARCHITECTURE.md) - System design, patterns, and technical decisions
- [**Operations Guide**](docs/OPERATIONS.md) - Project links, monitoring, and operational procedures

### 🚢 Deployment & Operations

- [**Deployment Guide**](docs/DEPLOYMENT.md) - How to deploy mobile app and backend services
